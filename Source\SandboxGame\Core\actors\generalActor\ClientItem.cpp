
#include "ClientItem.h"
#include "ImageMesh.h"
#include "BlockMesh.h"
#include "DefManagerProxy.h"
#include "world.h"
#include "WorldRender.h"
#include "BlockScene.h"
#include "PlayerControl.h"
#include "ActorLocoMotion.h"
#include "ActorAttrib.h"
#include "ActorBody.h"
#include "backpack.h"
#include "BlockMaterial.h"
#include "BlockMaterialMgr.h"
#include "SectionMesh.h"
#include "ClientActorManager.h"
#include "ActorCSProto.h"
#include "OgreUtils.h"
#include "Environment.h"
#include "special_blockid.h"
#include "WorldManager.h"
#include "ActorManager.h"
#include "CustomModelMgr.h"
#include "ObserverEventManager.h"
#include "OgreScriptLuaVM.h"
#include "ModelItemMesh.h"
#include "ClientInfoProxy.h"
#include "SandboxObject.h"
#include "SandboxCoreDriver.h"
#include "SandboxEventDispatcherManager.h"
#include "ItemLocoMotion.h"
#include "PermitsDef.h"
#include "SandboxListener.h"
#include "DieComponent.h"
#include "ClientInfoProxy.h"
#include "ThermalSpringManager.h"
#include "PlayerCheat.h"
#include "ItemIconManager.h"
#include "OgreEntity.h"
#include "FindComponent.h"
#include "UgcAssetMgr.h"
#include "UGCEntity.h"
#include "Pkgs/AssetTextureSettingLoader.h"
#include "GameNetManager.h"
#include "EffectComponent.h"
#include "GameScene/RainbowEffectProxy.h"

using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;

const float MOVE2PLAYER_TICKS = 10;
const float SECTIONMESH_SCALE = 1.0f/5.0f;
const float IMAGEMESH_SCALE = 1.0f/32.0f;

float g_x = 20.0f;
float g_y = 0;
float g_z = 0;
IMPLEMENT_SCENEOBJECTCLASS(ClientItem)
ClientItem::ClientItem()
{
	createEvent();

	SetBackPackGrid(m_ItemData, 0, 0);
	m_RootObj = MovableObject::Create();

	RemoveComponent(GetComponentByName("RiddenComponent"));
	CreateComponent<ItemLocoMotion>("ItemLocoMotion");
	cacheFindComponent(CreateComponent<FindComponent>("FindComponent"));
	getAttrib()->initHP(5);
	m_ProtoData = 0;
	m_ServerID = "";
	m_isEditing = false;
	m_InHotZone = false;
	m_gameMode = -1;
	m_spwantype = ANYWAY;
	m_particleEntity = nullptr;
	m_TriggerAnimID = 0;
	 m_AnimMode = 0;
	 m_disevent = false;
}

ClientItem::ClientItem(const BackPackGrid &grid) : m_ItemData(grid)
{
	createEvent();

	m_RootObj = MovableObject::Create();
	RemoveComponent(GetComponentByName("RiddenComponent"));
	CreateComponent<ItemLocoMotion>("ItemLocoMotion");
	cacheFindComponent(CreateComponent<FindComponent>("FindComponent"));
	getAttrib()->initHP(5);
	getAttrib()->initMaxHP(5);
	m_ProtoData = 0;
	m_ServerID = "";
	m_isEditing = false;
	m_InHotZone = false;
	m_gameMode = -1;
	m_spwantype = ANYWAY;
	m_particleEntity = nullptr;
	m_TriggerAnimID = 0;
	m_AnimMode = 0;
	m_disevent = false;
}
//事件注册
void ClientItem::createEvent()
{
	typedef ListenerFunctionRef<int&> ListenerGetDropItemNum;
	ListenerGetDropItemNum* listenerGetDropItemNum = SANDBOX_NEW(ListenerGetDropItemNum, [&](int& ret) -> void {
		ret = this->getDropItemNum();
	});
	Event2().Subscribe("getDropItemNum", listenerGetDropItemNum);

	typedef ListenerFunctionRef<float> ListenerFall;
	ListenerFall* listenerFall = SANDBOX_NEW(ListenerFall, [&](float fallh) -> void {
		this->fall(fallh);
	});
	Event2().Subscribe("fall", listenerFall);

	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("polaroidPictureStateChange");
	m_PhotoStateChangeCB = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("polaroidPictureStateChange", nullptr, [&](MNSandbox::SandboxContext context) -> MNSandbox::SandboxResult {
		if (getItemID() != ITEM_POLAROID_PHOTO)
			return MNSandbox::SandboxResult(nullptr, false);
		if (m_RenderObjs.size() == 0)
			return MNSandbox::SandboxResult(nullptr, false);

		BaseItemMesh* pItemMesh = m_RenderObjs[0];
		ImageMesh* pImageMesh = dynamic_cast<ImageMesh*>(pItemMesh);
		if (!pImageMesh)
			return MNSandbox::SandboxResult(nullptr, false);

		if (m_ItemData.userdata_str.size() == 0)
			return MNSandbox::SandboxResult(nullptr, false);

		jsonxx::Object infoObj;
		if (!infoObj.parse(m_ItemData.userdata_str))
			return MNSandbox::SandboxResult(nullptr, false);
		string path("");
		if (infoObj.has<jsonxx::String>("pngpath"))
			path = infoObj.get<jsonxx::String>("pngpath");
		if (path.size() == 0)
			return MNSandbox::SandboxResult(nullptr, false);

		MNSandbox::SandboxResult result = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TriggerObjLibManager_polaroidDropItemGetTexture",
			MNSandbox::SandboxContext(nullptr).SetData_String("photostrid", path));

		SharePtr<Texture2D> ptex = NULL;
		if (!result.IsSuccessed())
			ptex = GetAssetManager().LoadAsset<Texture2D>("items/unknown.png");
		else
			ptex = result.GetData_UserObject<SharePtr<Texture2D>>("texture2d");

		if (ptex.IsNull())
			return MNSandbox::SandboxResult(nullptr, false);

		IconDesc* photoIconDesc = ENG_NEW(IconDesc)();
		photoIconDesc->tex = ptex;
		photoIconDesc->color = Rainbow::ColorRGBA32::white;
		photoIconDesc->u = photoIconDesc->v = 1;
		photoIconDesc->width = ptex->GetOrginWidth() - 2 * 1;
		photoIconDesc->height = ptex->GetOrginHeight() - 2 * 1;
		//这里用完应该就可以删了，因为ImageMesh并没有持有IconDesc的相关东西
		pImageMesh->UpdateTexture(photoIconDesc);

		ENG_DELETE(photoIconDesc);

		return MNSandbox::SandboxResult(nullptr, true);
	});

	typedef ListenerFunctionRef<int&, int&> Listener1;
	Listener1* listener1 = SANDBOX_NEW(Listener1, [&](int& animid, int& animode) -> void {
		animid = this->GetAnimID();
		animode = this->GetAnimMode();
		});
	Event2().Subscribe("ClientItem_GetValue", listener1);
}
/*
ClientItem::ClientItem(int itemid, int num, int duration, int enchantnum, const int enchants[])
{
	SetBackPackGrid(m_ItemData, itemid, num, duration);
	m_ItemData.setEnchants(enchantnum, enchants);
	m_RootObj = SANDBOX_NEW(MovableObject);
	m_LocoMotion = SANDBOX_NEW(ItemLocoMotion, this);
	m_Attrib = SANDBOX_NEW(ActorAttrib,this);
	m_Attrib->m_fMaxHP = m_Attrib->m_Life = 5;
	m_ProtoData = 0;
	m_ServerID = "";
}*/

ClientItem::~ClientItem()
{
	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Unsubscribe("polaroidPictureStateChange", m_PhotoStateChangeCB);

	clearRenderObjs();
	DESTORY_GAMEOBJECT_BY_COMPOENT(m_particleEntity);

	DESTORY_GAMEOBJECT_BY_COMPOENT(m_RootObj);
}

void ClientItem::fall(float fall_dist)
{
}

int ClientItem::getObjType()const
{
	return OBJ_TYPE_DROPITEM;
}

void ClientItem::enterWorld(World *pworld)
{
	ClientActor::enterWorld(pworld);

	m_DelayPickTicks = 10;
	//yinqing add
	m_RootObj->AttachToScene(pworld->getScene());
	/*
	for(size_t i=0; i<m_RenderObjs.size(); i++)
	{
		m_RenderObjs[i]->attachToScene(m_pWorld->getScene());
	}*/
	if (g_WorldMgr) {
		m_gameMode = g_WorldMgr->getGameMode();
	}
	createRenderObjs();
}

void ClientItem::leaveWorld(bool keep_inchunk)
{
	/*
	for(size_t i=0; i<m_RenderObjs.size(); i++)
	{
		m_RenderObjs[i]->detachFromScene();
	}*/
	//yinqing add
	m_RootObj->DetachFromScene();

	ClientActor::leaveWorld(keep_inchunk);
}

//void ClientItem::onCull(MINIW::CullResult *presult, MINIW::CullFrustum *frustum)
//{
//	for(size_t i=0; i<m_RenderObjs.size(); i++)
//	{
//		presult->addRenderable(m_RenderObjs[i], RL_SCENE, NULL);
//	}
//}

void ClientItem::tick()
{
	ClientActor::tick();

	if(m_DelayPickTicks > 0) m_DelayPickTicks--;
	//if(m_LiveTicks >= 6000) setNeedClear();
	if (!m_disevent)
	{
		ObserverEvent_ActorItem obevent(getObjId(), getItemID(), getItemNum());
		WCoord itempos = CoordDivBlock(this->getPosition());
		obevent.SetData_Position(itempos.x, itempos.y, itempos.z);
		obevent.SetData_CustomDefault(GetItemSpawnType());
		ObserverEventManager::getSingleton().OnTriggerEvent("Item.Create", &obevent); // 掉落物创建
		m_disevent = true;

		////@soc2024
		//playMotion("item_137_blue",

		const ItemDef* itemdef = GetDefManagerProxy()->getItemDef(m_ItemData.m_ItemID);
		if (itemdef->QualityLevel > 0)//有品质的才有特效
		{
			auto effect = getEffectComponent();
			if (effect)
				effect->playBodyEffectForTrigger("white_glow", 1.0, 11000, 90);
			//effect->playBodyEffectForTrigger("item_1045_work", 0.5, 11000, 90);
		}
		//	effect->playBodyEffectForTrigger("item_137_white",1,11000,90);
		//playMotion("item_137_white",
		//	false,
		//	11000,
		//	1.00000000,
		//	90.0000000);//90s
	}

	if (!getFlagBit(ACTORFLAG_PERSISTENCE) && m_LiveTicks >= 4800 && m_pWorld->getOWID() != NEWBIEWORLDID) {
		ObserverEvent_ActorItem obevent(getObjId(), getItemID(), getItemNum());
		WCoord itempos = CoordDivBlock(this->getPosition());
		obevent.SetData_Position(itempos.x, itempos.y, itempos.z);
		GetObserverEventManager().OnTriggerEvent("Item.Disappear", &obevent);

		setNeedClear();
	}
	if (m_InHotZone)
	{
		int bakeid = GetDefManagerProxy()->getItemBakeTo(m_ItemData.getItemID());
		if (bakeid > 0)
		{
			WCoord itempos = CoordDivBlock(this->getPosition());
			setNeedClear();
			m_pWorld->spawnItem(itempos.x, itempos.y, itempos.z, bakeid, m_ItemData.getNum());
		}
	}
    #ifndef IWORLD_SERVER_BUILD
	ColourValue lighting = m_pWorld->getRender()->getLighting(WCoord(getPosition()));
	ColourValue lighting2 = m_pWorld->getRender()->getLighting(WCoord(getPosition()) + WCoord(0,20,0));
	if(lighting.r < lighting2.r) lighting.r = lighting2.r;
	if(lighting.g < lighting2.g) lighting.g = lighting2.g;
	if(lighting.b < lighting2.b) lighting.b = lighting2.b;

	/*Vector4f lightparam(0, 0, 0, 0);
	WCoord center = getPosition();
	center.y += getLocoMotion()->m_BoundHeight / 2;
	if (m_pWorld) m_pWorld->getBlockLightValue2(lightparam.x, lightparam.y, CoordDivBlock(center));*/

	for(size_t i=0; i<m_RenderObjs.size(); i++)
	{
		m_RenderObjs[i]->m_Lighting = lighting;
		//m_RenderObjs[i]->setInstanceData(lightparam);
	}
	#endif

	checkNeedResetDis();
}

void ClientItem::checkNeedResetDis() {
	if (m_gameMode == OWTYPE_GAMEMAKER && g_WorldMgr && g_WorldMgr->getGameMode() != m_gameMode) {
		m_gameMode = g_WorldMgr->getGameMode();
		WorldDesc* desc = GetClientInfoProxy()->getCurWorldDesc();
		bool bNew = (desc && desc->editorSceneSwitch > 0);
		if(bNew){
			for (size_t i = 0; i < m_RenderObjs.size(); i++)
			{
				m_RenderObjs[i]->SetVisibleDistance(16 * BLOCK_FSIZE);
			}
		}
	}
}

IMPLEMENT_SCENEOBJECTCLASS(AltarRewardItem)
bool AltarRewardItem::checkPermissionFailed(ClientPlayer *player)
{
    //检查是否有权限
    int uin = player->getUin();
    if(m_ItemData.userdata_str.length() > 0)
    {
        int limitUin = 0;
        sscanf(m_ItemData.userdata_str.c_str(), "%d", &limitUin);
        if(!(limitUin == 0 || limitUin == uin))//id 不匹配
            return true;
    }
    return ClientItem::checkPermissionFailed(player);
}

void ClientItem::addItemNum(int num)
{
	if (needClear() || (!m_ItemData.def)) return;

	m_ItemData.addNum(num);
	clearRenderObjs();
	createRenderObjs();
}

void ClientItem::SetOutline(UInt32 value)
{
	for (size_t i = 0; i < m_RenderObjs.size(); i++)
	{
		int modelType = m_RenderObjs[i]->getModelType();
		if (modelType == ITEM_MODEL_IMAGE)
			continue;

		ModelItemMesh* pmesh = dynamic_cast<ModelItemMesh*>(m_RenderObjs[i]);
		if (pmesh && pmesh->GetModel())
		{
			pmesh->GetModel()->SetOutline(value);
		}
		else
		{
			m_RenderObjs[i]->SetOutline(value);
		}
	}
}

bool ClientItem::checkPermissionFailed(ClientPlayer *player)
{
    if ( ROOM_SERVER_RENT != GetClientInfoProxy()->getRoomHostType())
    {	
		SandboxResult resultcanUseItem = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canUseItem",
			SandboxContext(nullptr).SetData_Number("uin", player->getUin()).SetData_Number("itemid", m_ItemData.getItemID()));
		bool canUseItemFlag = false;
		if (resultcanUseItem.IsExecSuccessed())
		{
			canUseItemFlag = resultcanUseItem.GetData_Bool();
		}
		if (!canUseItemFlag)
		{
			player->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 451);
			return true;
		}

		SandboxResult resultcanPermit = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canPermit",
			SandboxContext(nullptr)
			.SetData_Number("uin", player->getUin())
			.SetData_Number("itemid", m_ItemData.getItemID())
			.SetData_Number("bit", CS_PERMIT_PICK_ITEM));
		bool canPermitFlag = false;
		if (resultcanPermit.IsExecSuccessed())
		{
			canPermitFlag = resultcanPermit.GetData_Bool();
		}
		if (!canPermitFlag)
		{

			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_isHost",
				SandboxContext(nullptr).SetData_Number("uin", player->getUin()));
			bool isHostFlag = false;
			if (result.IsExecSuccessed())
			{
				isHostFlag = result.GetData_Bool();
			}
			if (isHostFlag)
				MINIW::ScriptVM::game()->callFunction("notifyAuthorityGameInfo2Player", "iiii", player->getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9725, int(CS_PERMIT_PICK_ITEM));
			else
				MINIW::ScriptVM::game()->callFunction("notifyAuthorityGameInfo2Player", "iiii", player->getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9720, int(CS_PERMIT_PICK_ITEM));
			return true;
		}
    }
    else
    {
        LOG_INFO("拾取道具");
		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_canCSPermit",
			SandboxContext(nullptr)
			.SetData_Number("uin", player->getUin())
			.SetData_Number("itemid", m_ItemData.getItemID())
			.SetData_Number("bit", CS_PERMIT_PICK_ITEM));
		bool canCSPermitFlag = false;
		if (result.IsExecSuccessed())
		{
			canCSPermitFlag = result.GetData_Bool();
		}
		if (!canCSPermitFlag)
		{
			LOG_INFO("拾取道具提示");

			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_isHost",
				SandboxContext(nullptr).SetData_Number("uin", player->getUin()));
			bool isHostFlag = false;
			if (result.IsExecSuccessed())
			{
				isHostFlag = result.GetData_Bool();
			}
			if (isHostFlag)
				MINIW::ScriptVM::game()->callFunction("notifyAuthorityGameInfo2Player", "iiii", player->getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9725, int(CS_PERMIT_PICK_ITEM));
			else
				MINIW::ScriptVM::game()->callFunction("notifyAuthorityGameInfo2Player", "iiii", player->getUin(), int(PLAYER_NOTIFYINFO_TIPS), 9639, int(CS_PERMIT_PICK_ITEM));
			return true;
		}
    }
    return false;
}

void ClientItem::onCollideWithPlayer(ClientActor *player)
{
	//LOG_INFO("onCollideWithPlayer：");
	if(m_DelayPickTicks > 0) return;
	if (g_WorldMgr && g_WorldMgr->isUGCEditMode()) return;
	
	auto pTempPlayer = dynamic_cast<ClientPlayer*>(player);
	if (nullptr != pTempPlayer)
	{
		// 如果是家园掉落物(一个重要标记就是有服务器id)--单独处理
		std::string serverid = getServerID();
		if (!serverid.empty())
		{
			SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().
				Emit("Homeland_pickUpDropItem", SandboxContext(nullptr).
					SetData_String("serverid", serverid).
					SetData_Number("objid", getObjId()).
					SetData_Number("uin", pTempPlayer->getUin()).
					SetData_Number("itemid", GetItemId()));
			if (result.IsSuccessed())
			{
				setPickOnce(true);
				return;
			}
		}

		if (checkPermissionFailed(pTempPlayer))
			return;

		if(GetWorldManagerPtr() && GetWorldManagerPtr()->isGodMode())
		{
			auto def = GetDefManagerProxy()->getItemDef(getItemID());
			if (def && def->MeshType == VEHICLE_GEN_MESH && pTempPlayer->getBackPack()->getShorCutEmptyGridNum() > 0) {
				//bCanAddToBackPack = true;
			}
			else {
				pTempPlayer->onPickupItem(this);
				onSubtractItem(getItemNum(), 10);
				return;
			}
		}

		int nitem = getItemNum();
		int itemid = getItemID();
		if (nitem <= 0)
		{
			setNeedClear(10);
			return;
		}
		if (!pTempPlayer->isAttrShapeShift() && pTempPlayer->getBody()->getMutateMob() > 0) return; //变身不能拾取


		if (!pTempPlayer->checkActionAttrState(ENABLE_PICKUP))
		{
			pTempPlayer->notifyGameInfo2Self(PLAYER_NOTIFYINFO_TIPS, 14008);
			return;
		}
		if (m_ItemData.isAttracted) return;

		GridCopyData gridcopydata(&m_ItemData);
		gridcopydata.resid = itemid;
		gridcopydata.num = nitem;
		std::string tempstr;
		// 染色方块改为掉落对应颜色方块（使用默认方块模型并调整颜色） by:Jeff 2022/12/14
		// 染色的方块用默认方块作为拾取物
		if (itemid < SOC_BLOCKID_MAX)//SOC 方块ID 范围 0-4095 不支持扩展id
		{
			BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(itemid);
			if (mtl && mtl->isColorableBlock() && m_ProtoData)
			{
				tempstr = std::to_string(m_ProtoData);
				gridcopydata.userdata_str = tempstr.c_str();
			}
		}
		gridcopydata.gridix = -1;
		int num = 0;
		{
			AntiSetting::PickupRecorder recorder(getObjId());
			num = pTempPlayer->getBackPack()->addItemWithPickUp_byGridCopyData(gridcopydata);
			if (num == 0) return;
		}

		MNSandbox::GetGlobalEvent().Emit<const int, const int>("StatisticRainforest_onCondition_GetProp", itemid, pTempPlayer->getUin());

		pTempPlayer->addAchievement(1, ACHIEVEMENT_PICKITEM, m_ItemData.def->ID, num);
		pTempPlayer->updateTaskSysProcess(TASKSYS_GAIN_ITEM, m_ItemData.def->ID, 0, num);
		if (m_pWorld->getOWID() == NEWBIEWORLDID)
			pTempPlayer->checkNewbieWorldTask(m_ItemData.def->ID);
		m_ItemData.isAttracted = true;
		pTempPlayer->onPickupItem(this);
		onSubtractItem(num, 10);

		// 获取许愿星方块，上报
		if ((GetItemId() == 200430 || GetItemId() == 200431))
		{
			if (pTempPlayer == g_pPlayerCtrl)
			{
				MINIW::ScriptVM::game()->callFunction("FlowersActivity_GameEventReport", "sii", "meet_wishstar", 0, 0);
			}
			else
			{
				game::common::PB_MeteorShowerInfo hc;
				hc.set_type(2);
				GetGameNetManagerPtr()->sendBroadCast(PB_METEOR_SHOWER_HC, hc);
			}
		}

		pTempPlayer->pickUpItemOnTrigger(getObjId(), itemid, num);
		if (itemid == BLOCK_DRIFTBOTTLE || itemid == BLOCK_FAR_DRIFTBOTTLE)
		{
			MINIW::ScriptVM::game()->callFunction("DriftBottleUseReport", "isii", pTempPlayer->getUin(), m_ItemData.userdata_str.c_str(), 1, 2);
		}
		ObserverEvent_ActorItem obevent(pTempPlayer->getObjId(), itemid, num, (long long)getObjId());
		ItemDef* itemdef = GetDefManagerProxy()->getItemDef(getItemID());
		WCoord pos = this->getPosition();
		pos = CoordDivBlock(pos);
		obevent.SetData_Position(pos.x, pos.y, pos.z);
		// 插件方块道具被拾取事件
		if (itemdef && itemdef->Type == ITEM_TYPE_BLOCK)  // 方块大类
		{
			obevent.SetData_Block(itemid);
		}
		GetObserverEventManager().OnTriggerEvent("Item.Pickup", &obevent);
	}
}

void ClientItem::onSubtractItem(int num, int clearticks)
{
	if(num < getItemNum())
	{
		BackPackGrid grid(m_ItemData);
		grid.setNum(m_ItemData.getNum() - num);
		ActorManager* actorMgr = dynamic_cast<ActorManager*>(m_pWorld->getActorMgr());
		if (!actorMgr) return ;
		actorMgr->spawnItem(getLocoMotion()->getPosition(), grid);
	}

	setNeedClear(clearticks);
}

void ClientItem::update(float dtime)
{
	getLocoMotion()->update(dtime);
	WorldPos pos = getLocoMotion()->getFramePosition();

	if(!needClear())
	{
		float angle = 60.0f*m_LiveTicks*GAME_TICK_TIME;
		Quaternionf quat = AxisAngleToQuaternionf(Vector3f::yAxis, Deg2Rad(angle));;
		//quat.setAxisAngleY(angle);
		m_RootObj->SetRotation(quat);

		pos.y += WorldPos::Flt2Fix((BLOCK_FSIZE/10.0f)*(Rainbow::Sin(angle) + 2.5f));
	}

	m_RootObj->SetPosition(pos);

	unsigned int dtick = TimeToTick(dtime);
	for(size_t i=0; i<m_RenderObjs.size(); i++)
	{
		//m_RenderObjs[i]->updateWorldCache();
		m_RenderObjs[i]->UpdateTick(dtick);
	}

	if (m_particleEntity)
	{
		//m_particleEntity->updateWorldCache();
		m_particleEntity->UpdateTick(dtick);
	}
}

int ClientItem::getItemID()
{
	return m_ItemData.getItemID();
}

int ClientItem::getItemNum()
{
	return m_ItemData.getNum();
}

int ClientItem::getDropItemNum()
{
	return getItemNum();
}

int ClientItem::GetItemId()
{
	return getItemID();
}

static int GetNumRenderObj(int itemnum)
{
	if(itemnum == 1) return 1;
	else if(itemnum < 6) return 2;
	else if(itemnum < 21) return 3;
	else if(itemnum < 43) return 4;
	else return 5;
}

static Rainbow::Vector3f ObjOffset[5] = 
{
	Rainbow::Vector3f(50.0f, 0,     50.0f), 
	Rainbow::Vector3f(80.0f, 15.0f, 20.0f),
	Rainbow::Vector3f(37.0f, 32.0f, 0.0f),
	Rainbow::Vector3f(-7.0f, 32.0f, -15.0f),
	Rainbow::Vector3f(21.0f, -38.0f, 23.0f)
};

bool ClientItem::mergeItem(ClientItem *item)
{
	if (!item)
	{
		return false;
	}
	if (!needMergeItem() || !item->needMergeItem())
	{
		return false;
	}
	if(needClear() || item->needClear() || (!m_ItemData.def)) return false;
	if(getItemID() != item->getItemID()) return false;
	if(getItemNum() + item->getItemNum() > m_ItemData.def->StackMax) return false;
	if(getItemNum() < item->getItemNum()) return item->mergeItem(this);

	if (m_pWorld->IsUGCEditMode())
	{
		SandboxCoreDriver::GetInstance().GetManagers().GetEventDispatcherMgr().Emit("UGCMergeItem", SandboxContext(nullptr).SetData_Bool("isBefore", true).SetData_String("objId", toString(item->getObjId())));

		m_ItemData.addNum(item->m_ItemData.getNum());
		if (item->m_DelayPickTicks > m_DelayPickTicks) m_DelayPickTicks = item->m_DelayPickTicks;
		if (item->m_LiveTicks < m_LiveTicks) m_LiveTicks = item->m_LiveTicks;

		item->setNeedClear();
		createRenderObjs();

		SandboxCoreDriver::GetInstance().GetManagers().GetEventDispatcherMgr().Emit("UGCMergeItem", SandboxContext(nullptr).SetData_Bool(false).SetData_String(toString(getObjId())));
		return true;
	} 


	m_ItemData.addNum(item->m_ItemData.getNum());
	if(item->m_DelayPickTicks > m_DelayPickTicks) m_DelayPickTicks = item->m_DelayPickTicks;
	if(item->m_LiveTicks < m_LiveTicks) m_LiveTicks = item->m_LiveTicks;

	item->setNeedClear();
	createRenderObjs();
	return true;
}

void ClientItem::searchForOtherItemsNearby()
{
	std::vector<IClientActor *>actors;
	CollideAABB box;
	getCollideBox(box);
	box.expand(BLOCK_SIZE/2, 0, BLOCK_SIZE/2);

	m_pWorld->getActorsOfTypeInBox(actors, box, OBJ_TYPE_DROPITEM, getItemID());
	for(size_t i=0; i<actors.size(); i++)
	{
		ClientItem *other = static_cast<ClientItem *>(actors[i]);

		if(other != this)
		{
			mergeItem(other);
		}
	}
}

void ClientItem::onDie()
{
	auto DieComp = SureDieComponent();
	if (DieComp) DieComp->onDieBase();

	ObserverEvent_ActorItem obevent(getObjId(), getItemID(), getItemNum());
	WCoord itempos = CoordDivBlock(this->getPosition());
	obevent.SetData_Position(itempos.x, itempos.y, itempos.z);
	GetObserverEventManager().OnTriggerEvent("Item.Disappear", &obevent);

}
BaseItemMesh* ClientItem::createImageMesh(int itemid)
{
	BaseItemMesh *pmesh = NULL;
	IconDesc* iconDesc = ItemIconManager::GetInstance().getImageMeshData(itemid);
	if (iconDesc)
		pmesh = ImageMesh::Create(iconDesc);


	return pmesh;
}

BaseItemMesh* ClientItem::createDynamicImageMesh(int itemid, const std::string& userdatastr)
{
	//每张图片都是唯一的一张，所以要去每张都创建
	if (itemid != ITEM_POLAROID_PHOTO)
		return NULL;
	if (userdatastr.size() == 0)
		return NULL;
	jsonxx::Object infoObj;
	if (!infoObj.parse(userdatastr))
		return NULL;
	string path("");
	if (infoObj.has<jsonxx::String>("pngpath"))
		path = infoObj.get<jsonxx::String>("pngpath");

	if (path.size() == 0)
		return NULL;

	MNSandbox::SandboxResult result = MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("TriggerObjLibManager_polaroidDropItemGetTexture",
		MNSandbox::SandboxContext(nullptr).SetData_String("photostrid", path));

	SharePtr<Texture2D> ptex = NULL;
	if (!result.IsSuccessed())
	{
		ptex = GetAssetManager().LoadAsset<Texture2D>("items/unknown.png");
	}
	else
	{
		ptex = result.GetData_UserObject<SharePtr<Texture2D>>("texture2d");
	}

	if (ptex.IsNull())
		return NULL;

	IconDesc *photoIconDesc = ENG_NEW(IconDesc)();
	photoIconDesc->tex = ptex;
	photoIconDesc->color = Rainbow::ColorRGBA32::white;
	photoIconDesc->u = photoIconDesc->v = 1;
	photoIconDesc->width = ptex->GetOrginWidth() - 2 * 1;
	photoIconDesc->height = ptex->GetOrginHeight() - 2 * 1;
	ImageMesh *pMesh = ImageMesh::Create(photoIconDesc);
	//这里用完应该就可以删了，因为ImageMesh并没有持有IconDesc的相关东西
	ENG_DELETE(photoIconDesc);

	return pMesh;
}

void ClientItem::createRenderObjs()
{
	#ifndef IWORLD_SERVER_BUILD
	int nobj = GetNumRenderObj(getItemNum());
	int newobjs = nobj - int(m_RenderObjs.size());

	for(int i=0; i<newobjs; i++)
	{
		int blockid = getItemID();
		BaseItemMesh *obj;

		std::string colordata = "";
		// 染色的方块用默认方块作为掉落物
		// 染色方块改为掉落对应颜色方块（使用默认方块模型并调整颜色） by:Jeff 2022/12/14
		if (blockid < SOC_BLOCKID_MAX)//SOC 方块ID 范围 0-4095 不支持扩展id
		{
			BlockMaterial *mtl = g_BlockMtlMgr.getMaterial(blockid);
			if (mtl && mtl->isColorableBlock() && m_ProtoData)
			{
				colordata = std::to_string(m_ProtoData);
			}
		}
		
		if(GetDefManagerProxy()->getItemDef(blockid))
		{
			obj = createItemModel(blockid, ITEM_MODELDISP_DROP, 1.0f, 0, THROW_MESH, colordata == "" ? m_ItemData.getUserdataStr() : colordata, kLayerIndexCustom_DropItem);
			if (obj) 
			{
				obj->GetGameObject()->SetLayer(Rainbow::kLayerIndexCustom_DropItem);
				//obj->SetSRTFather(m_RootObj, 0);
				obj->GetTransform()->SetParent(m_RootObj->GetTransform());
				obj->SetVisibleDistance(16*BLOCK_FSIZE);
				if(m_ItemData.hasRuneOrEnchants() || (m_ItemData.def && m_ItemData.def->IconEffect>0))
				{
					obj->setOverlay(0);
				}
				m_RenderObjs.push_back(obj);
			}
		}
	}

	Rainbow::Vector3f center(0,0,0);
	for(int i=0; i<nobj; i++)
	{
		center += ObjOffset[i];
	}
	center /= float(nobj);
	
	WorldDesc* desc = GetClientInfoProxy()->getCurWorldDesc();
	bool bNew = (desc && desc->editorSceneSwitch > 0);

	for(int i=0; i<(int)m_RenderObjs.size(); i++)
	{
		Rainbow::Vector3f dp = (ObjOffset[i]-center)*SECTIONMESH_SCALE;
		m_RenderObjs[i]->SetOffsetPosition(dp);
		if (bNew && g_WorldMgr && g_WorldMgr->getGameMode() == OWTYPE_GAMEMAKER)
		{
			m_RenderObjs[i]->SetVisibleDistance(64 * BLOCK_FSIZE);
		}
	}
	if (bNew)
	{
		setScale(2.0f);
	}
	#endif
}

void ClientItem::clearRenderObjs()
{
	for (size_t j = 0; j < m_RenderObjs.size(); j++)
	{
		m_RenderObjs[j]->DestroyGameObject();
	}
	m_RenderObjs.clear();
}

static float s_ItemModelScales[MAX_ITEM_MODEL][MAX_ITEM_MODELDISP] = 
{
	//	DROP				CAMERA			HAND			SCENE
/*Block*/	{0.2f,			0.31f,			0.4f,			1.0f},
/*Image*/	{1.0f/32.0f,	1.0f/16.0f,		0.08f,			1.0f/32.0f},
/*Omod*/	{0.5f,			1.0f,			2.0f,			1.0f},
};

int ClientItem::getRealModelItemId(int itemid)
{
	const ItemDef *itemdef = GetDefManagerProxy()->getItemDef(itemid);
	if(itemdef && !itemdef->Model.empty())
	{
		const char *model = itemdef->Model.c_str();

		if(model[0] == '*')
		{
			int id = atoi(model+1);
			return id > 0 ? id : itemid;
		}
	}

	return itemid;
}

ModelItemMesh *ClientItem::createItemMeshModel(int itemid, ITEM_MODELDISP_TYPE disptype, float customscale, int userdata)
{
	ModelItemMesh *pmesh = NULL;
	int itemId = getRealModelItemId(itemid);
	auto modelItemMesh = ModelItemMesh::Create(itemId, 0, userdata);
	modelItemMesh->switchModel(0, userdata);
	pmesh = modelItemMesh;
	//pmesh = ENG_NEW(ModelItemMesh)(itemId, 0, userdata);
	float s = s_ItemModelScales[pmesh->getModelType()][disptype] * customscale;
	pmesh->SetScale(Rainbow::Vector3f(s, s, s));
	return pmesh;
}

BaseItemMesh* ClientItem::createItemModel(int itemid, ITEM_MODELDISP_TYPE disptype, float customscale/* =1.0f */, int userdata/* =0 */, ITEM_MESH_TYPE meshtype/* =NORMAL_MESH */, std::string userdatastr/*""*/, int layer)
{
	ItemDef *itemdef = GetDefManagerProxy()->getItemDef(itemid);
	if (!itemdef)
		return NULL;
	float tableScale = 1;
	switch (disptype)
	{
		case ITEM_MODELDISP_CAMERA:
		{
			if (itemdef->WieldScale != 0)
				tableScale = itemdef->WieldScale;
			break;
		}
		case ITEM_MODELDISP_HAND:
		{
			if (itemdef->ThirdPersonScale != 0)
				tableScale = itemdef->ThirdPersonScale;
			break;
		}
		case ITEM_MODELDISP_DROP:
		{
			if (itemdef->DropScale != 0)
				tableScale = itemdef->DropScale;
		}
		default:
			break;
	}
	BaseItemMesh *pmesh = NULL;
	if (itemdef && itemdef->MeshType == UGC_MODEL_GEN_MESH)
	{
		std::string modelParam = itemdef->ModelComp.c_str();
		std::string modelStr = "";
		std::string modelType = "";
		int modelId = 0;
		int level = -1;
		fromString(userdatastr, level);
		Rainbow::UGCEntity* entity = UgcAssetMgr::GetInstance().CreateEntity(modelParam, modelType, modelStr, modelId, layer, level, itemid);
		if (modelType == "custom")
		{
			//自定义模型
			Rainbow::FixedString filename = itemdef->Model;
			itemdef->Model = modelStr.c_str();
			itemdef->MeshType = CUSTOM_GEN_MESH;
			pmesh = createItemModel2(itemid, disptype, customscale, userdata, meshtype, userdatastr);
			itemdef->Model = filename;
			itemdef->MeshType = UGC_MODEL_GEN_MESH;
		}
		else if (modelType == "fullycustom")
		{
			//自定义模型
			Rainbow::FixedString filename = itemdef->Model;
			itemdef->Model = modelStr.c_str();
			itemdef->MeshType = FULLY_CUSTOM_GEN_MESH;
			pmesh = createItemModel2(itemid, disptype, customscale, userdata, meshtype, userdatastr);
			itemdef->Model = filename;
			itemdef->MeshType = UGC_MODEL_GEN_MESH;
		}
		else if (modelType == "prefab")
		{
			if (entity)
			{
				float s = tableScale * customscale;
				ModelItemMesh* mesh = ModelItemMesh::Create(itemid, 0, userdata, meshtype);
				mesh->setIsUGCEntity(true);
				mesh->switchUgcModel(entity, s, modelType);
				pmesh = mesh;
			}
		}
		else if (modelType == "obj")
		{
			if (entity)
			{
				//用omod的scale？
				float s = tableScale * customscale;
				ModelItemMesh* mesh = ModelItemMesh::Create(itemid, 0, userdata, meshtype);
				mesh->switchUgcModel(entity, s, modelType);
				mesh->setIsUGCEntity(true);
				pmesh = mesh;
			}
		}
		else if (modelType == "omod")
		{
			if (entity)
			{
				//用omod的scale？
				float s = tableScale * customscale;
				ModelItemMesh* mesh = ModelItemMesh::Create(itemid, 0, userdata, meshtype);
				mesh->switchUgcModel(entity, s, modelType);
				mesh->setIsUGCEntity(true);
				pmesh = mesh;
			}
		}
		else if (modelType == "shape")
		{
			if (entity)
			{
				//用方块的scale？
				float s = tableScale * customscale;
				ModelItemMesh* mesh = ModelItemMesh::Create(itemid, 0, userdata, meshtype);
				mesh->switchUgcModel(entity, s, modelType);
				mesh->setIsUGCEntity(true);
				pmesh = mesh;
			}
		}
		else if (modelType == "block" || modelType == "mob" || modelType == "item")
		{
			if (modelId > 0)
			{
				pmesh = createItemModel2(modelId, disptype, customscale, userdata, meshtype, userdatastr);
			}
		}
	}
	else
	{
		pmesh = createItemModel2(itemid, disptype, customscale, userdata, meshtype, userdatastr);
	}
	return pmesh;
}

BaseItemMesh* ClientItem::createItemModel2(int itemid, ITEM_MODELDISP_TYPE disptype, float customscale/* =1.0f */, int userdata/* =0 */, ITEM_MESH_TYPE meshtype/* =NORMAL_MESH */, std::string userdatastr/*""*/)
{
    OPTICK_EVENT();
	int itemId = getRealModelItemId(itemid);
	const ItemDef *itemdef = GetDefManagerProxy()->getItemDef(itemId);
	const BlockDef* blockdef = GetDefManagerProxy()->getBlockDef(itemId);
	/*
	IconDesc *icondesc = g_BlockMtlMgr.findItemIconDesc(itemid);
	if(icondesc == NULL)
	{
	LOG_SEVERE("can not find item icon with id: %d", itemid);
	itemid = 11001;
	icondesc = g_BlockMtlMgr.findItemIconDesc(itemid);
	}*/
	//从表格读取缩放值
	float tableScale = 1;
	BaseItemMesh *pmesh = NULL;
	bool isUseWeaponSkin = false;
	if (itemdef != nullptr)
	{
		if (blockdef != nullptr && disptype == ITEM_MODELDISP_CAMERA && (itemdef->MeshType == ICON_GEN_MESH || itemdef->MeshType == BLOCK_GEN_MESH) )
		{
			// 镜头前， block.cvs中配置的方块全部尝试使用blockmesh，如果没有使用iconmesh兜底显示；
			// 这里不处理block meshType > 3的方块
			BlockMaterial* pmtl = g_BlockMtlMgr.getMaterial(itemId);
			SectionMesh* protomesh = nullptr;
			if (IsDyeableBlock(itemId) && userdatastr != "")
			{
				//染色方块模型显示  by:Jeff 2022/12/14
				int colordata = 0;
				sscanf(userdatastr.c_str(), "%d", &colordata);
				//SectionMesh* protomesh = NULL;
				if (pmtl != NULL)
				{
					pmtl->Init();
					protomesh = pmtl->getBlockProtoMesh(colordata);
				}
			}
			else
			{
				//SectionMesh* protomesh = NULL;
				if (pmtl != NULL)
				{
					pmtl->Init();
					protomesh = pmtl->getBlockProtoMesh(userdata);
				}
			}

			if (protomesh)
			{
				BlockMesh* blockmesh = BlockMesh::Create(protomesh);
				blockmesh->setCenter(Rainbow::Vector3f(BLOCK_SIZE / 2.0f, 50, BLOCK_SIZE / 2.0f));
				blockmesh->setLightDir(Rainbow::Vector3f(1.0f, -1.0f, 1.0f));
				blockmesh->SetCustomMaterial();
				if (disptype == ITEM_MODELDISP_CAMERA || disptype == ITEM_MODELDISP_HAND)
				{
					blockmesh->SetUseBlockVertexLight(1.0f);
				}
				pmesh = blockmesh;
				if (pmtl && (strcmp(pmtl->getGeomName().c_str(), "windows") == 0))
				{
					pmesh->SetPosition(WorldPos(0, 0, 260));
				}
			}
			else
			{
				pmesh = createImageMesh(itemid);
			}
		}
		//用于图标生成的模型,    
		else if (itemdef->MeshType == ICON_GEN_MESH)
		{
			if (itemId == ITEM_POLAROID_PHOTO)
				pmesh = createDynamicImageMesh(itemId, userdatastr);
			else
				pmesh = createImageMesh(itemId);
		}
		//用于模型item
		else if (itemdef->MeshType == OMOD_GEN_MESH || itemdef->MeshType == CUSTOM_GEN_MESH || itemdef->MeshType == FULLY_CUSTOM_GEN_MESH || itemdef->MeshType == IMPORT_MODEL_GEN_MESH)
		{
			auto modelItemMesh = ModelItemMesh::Create(itemId, 0, userdata, meshtype);
			modelItemMesh->switchModelUserData(0, userdata, userdatastr);
			
			//auto level = itemdef->QualityLevel;
			//if (level >= 0 && level <= 7)
			//{
			//	//品质等级（0=无色 1=浅灰色,2=绿色，3=蓝色，4=紫色，5=橙色，6=红色，7=白金）
			//	static Rainbow::ColourValue color[8] = {
			//			Rainbow::ColourValue(0, 0, 0),
			//			Rainbow::ColourValue(0.8, 0.8, 0.8),
			//			Rainbow::ColourValue(0, 1, 0),
			//			Rainbow::ColourValue(0, 0, 1),
			//			Rainbow::ColourValue(0.5, 0, 0.5),
			//			Rainbow::ColourValue(1, 0.5, 0),
			//			Rainbow::ColourValue(1, 0, 0),
			//			Rainbow::ColourValue(0.95, 0.95, 0.95),
			//	};
			//	modelItemMesh->GetModel()->SetOverlayColor(&color[level]);
			//}
			pmesh = modelItemMesh;
		}
		else if (itemdef->MeshType == VEHICLE_GEN_MESH)
		{
			auto modelItemMesh = ModelItemMesh::Create(itemId, userdatastr, 0, userdata, meshtype);
			modelItemMesh->switchModel(userdatastr);
			pmesh = modelItemMesh;
			//pmesh = ENG_NEW(ModelItemMesh)(itemId, userdatastr, 0, userdata, meshtype);
			pmesh->SetScale(Rainbow::Vector3f(5.0f, 5.0f, 5.0f));
		}
		else if (itemdef->MeshType == PREFAB_GEN_MESH)
		{
			auto modelItemMesh = ModelItemMesh::Create(itemId, 0, userdata, meshtype);
			modelItemMesh->switchModelByPrefb(0, userdata);
			pmesh = modelItemMesh;
		}
		//用于BlockItem
		else if (itemdef->MeshType == BLOCK_GEN_MESH || itemdef->MeshType == 3)
		{
			BlockMaterial *pmtl = g_BlockMtlMgr.getMaterial(itemId);
			SectionMesh* protomesh = nullptr;
			if (IsDyeableBlock(itemId) && userdatastr != "")
			{
				//染色方块模型显示  by:Jeff 2022/12/14
				int colordata = 0;
				sscanf(userdatastr.c_str(), "%d", &colordata);
				//SectionMesh* protomesh = NULL;
				if (pmtl != NULL)
				{
					pmtl->Init();
					protomesh = pmtl->getBlockProtoMesh(colordata);
				}
			}
			else
			{
				//SectionMesh* protomesh = NULL;
				if (pmtl != NULL)
				{
					pmtl->Init();
					protomesh = pmtl->getBlockProtoMesh(userdata);
				}
			}

			if (protomesh)
			{
				BlockMesh* blockmesh = BlockMesh::Create(protomesh);
				blockmesh->setCenter(Rainbow::Vector3f(BLOCK_SIZE / 2.0f, 50, BLOCK_SIZE / 2.0f));
				blockmesh->setLightDir(Rainbow::Vector3f(1.0f, -1.0f, 1.0f));
				blockmesh->SetCustomMaterial();
				if (disptype == ITEM_MODELDISP_CAMERA || disptype == ITEM_MODELDISP_HAND)
				{
					blockmesh->SetUseBlockVertexLight(1.0f);
				}
				pmesh = blockmesh;
				if (pmtl && (strcmp(pmtl->getGeomName().c_str(), "windows") == 0))
				{
					pmesh->SetPosition(WorldPos(0, 0, 260));
				}
			}
			else {
				pmesh = createImageMesh(itemid);
			}
		}
		else
		{
			int blockid = itemdef->MeshType;
			BlockMaterial *pmtl = g_BlockMtlMgr.getMaterial(blockid);
			SectionMesh *protomesh = pmtl == NULL ? NULL : pmtl->getBlockProtoMesh(userdata);
			if (protomesh)
			{
				BlockMesh* blockmesh = BlockMesh::Create(protomesh);
				blockmesh->setCenter(Rainbow::Vector3f(BLOCK_SIZE / 2.0f, 50, BLOCK_SIZE / 2.0f));
				blockmesh->setLightDir(Rainbow::Vector3f(1.0f, -1.0f, 1.0f));
				pmesh = blockmesh;
			}
			else {
				pmesh = createImageMesh(blockid);
			}
		}

		switch (disptype)
		{
		case ITEM_MODELDISP_CAMERA:
			if (itemdef->WieldScale != 0)
				tableScale = itemdef->WieldScale;
			break;
		case ITEM_MODELDISP_HAND:
			if (itemdef->ThirdPersonScale != 0)
				tableScale = itemdef->ThirdPersonScale;
			break;
		case ITEM_MODELDISP_DROP:
			if (itemdef->DropScale != 0)
				tableScale = itemdef->DropScale;
		default:
			break;
		}
	}
	else
	{
		pmesh = createImageMesh(itemid);
	}
	if (!pmesh)
		return NULL;

	float s = 1.0f;
	if (disptype >= 0 && disptype < MAX_ITEM_MODELDISP)
		s = s_ItemModelScales[pmesh->getModelType()][disptype] * customscale * tableScale;

	if ((itemdef && itemdef->MeshType != CUSTOM_GEN_MESH && itemdef->MeshType != VEHICLE_GEN_MESH && itemdef->MeshType != FULLY_CUSTOM_GEN_MESH) ||
		isUseWeaponSkin)
	{
		//用户导入模型，可能会有特别大的模型，适当缩小，只针对手持
		if (disptype == ITEM_MODELDISP_HAND && itemdef->MeshType == IMPORT_MODEL_GEN_MESH)
		{
			auto* pModelMesh = dynamic_cast<ModelItemMesh*>(pmesh);
			if (pModelMesh && pModelMesh->GetModel())
			{
				auto* pRealModel = pModelMesh->GetModel();
				BoxSphereBound bound;
				pRealModel->getLocalBounds(bound);
				if (bound.m_Radius > 100.0f)
				{
					float curS = clamp(100.0f / bound.m_Radius, 0.2f, 1.0f);
					pRealModel->SetScale(Rainbow::Vector3f(curS, curS, curS));
				}
			}
		}

		pmesh->SetScale(Rainbow::Vector3f(s, s, s));
	}


	if (disptype == ITEM_MODELDISP_HAND)
	{
		const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(itemId);
		if (tooldef)
		{
			// test-by:liya 去除矛模型所有代码处理旋转
			//if (itemdef && itemdef->MeshType != CUSTOM_GEN_MESH && tooldef->Type == 6 && (tooldef->Level == 2 || tooldef->Level == 4)/* || tooldef->Type==1 && tooldef->Level==5 || tooldef->Type==2 && tooldef->Level==5*/)
			// 矛
			//if (itemdef && itemdef->MeshType != CUSTOM_GEN_MESH && tooldef->SubType == 4)
			//{
			//	pmesh->SetRotation(0, -90, 0);
			//	//if(tooldef->Level == 4) pmesh->SetPosition(Rainbow::Vector3f(g_x, g_y, g_z));
			//}
			//else 
				if (itemdef && itemdef->MeshType != CUSTOM_GEN_MESH && itemId == ITEM_IMPULSE)
			{
				pmesh->SetRotation(0, -90, -90);
			}
			else if (itemdef && itemdef->MeshType != CUSTOM_GEN_MESH && tooldef->Type == 29)
			{
				pmesh->SetRotation(0, -90, 0);
			}
		}
		if (IsArcBlock(itemId))
		{
			pmesh->SetRotation(0, -90, 0);
		}

		if (IsTriangularPrismBlock(itemId))
		{
			int yaw, pitch, roll;
			MINIW::ScriptVM::game()->callFunction("GetBlockTriangularPrismEular", ">iii", &yaw, &pitch, &roll);
			pmesh->SetRotation(float(yaw), float(pitch), float(roll));
		}

		if (IsVoidMelonBlock(itemId))
		{
			int yaw, pitch, roll;
			MINIW::ScriptVM::game()->callFunction("BlockVoidMelon_GetEular", ">iii", &yaw, &pitch, &roll);
			pmesh->SetRotation(float(yaw), float(pitch), float(roll));
		}
	}
	return pmesh;
}

bool ClientItem::supportSaveToPB()
{
	return true;
}

int ClientItem::saveToPB(PB_GeneralEnterAOIHC* pb)
{
	PB_ActorItem* actorItem = pb->mutable_actoritem();
	return saveItemToPB(actorItem);
}

int ClientItem::saveItemToPB(PB_ActorItem* actorItem)
{
	PB_ActorCommon* actorCommon = actorItem->mutable_basedata();
	savePBActorCommon(actorCommon);
	for (int i = 0; i < m_ItemData.getNumEnchant(); ++i)
	{
		actorItem->add_enchants(m_ItemData.getEnchants()[i]);
	}
	const GridRuneData& runeData = m_ItemData.getRuneData();
	if (runeData.getRuneNum() > 0)
		runeData.save(actorItem->mutable_runes());

	if (m_ItemData.def)
	{
		actorItem->set_itemid(m_ItemData.def->ID);
		actorItem->set_num(m_ItemData.getNum());
		if (m_ItemData.getUserdataStr() != "")
			actorItem->set_userdatastr(m_ItemData.getUserdataStr());
		else if (IsDyeableBlock(m_ItemData.def->ID) && m_ProtoData != 0)
		{
			//染色方块同步  by:Jeff 2023/2/2
			actorItem->set_userdatastr(std::to_string(m_ProtoData));
		}
	}
	else
	{
		actorItem->set_itemid(224);
		actorItem->set_num(1);
	}
	actorItem->set_durable(m_ItemData.getDuration());
	actorItem->set_maxdurable(m_ItemData.getMaxDuration());
	actorItem->set_toughness(m_ItemData.getToughness());
	actorItem->set_delayticks(m_DelayPickTicks);
	actorItem->set_serverid(m_ServerID);

	m_ItemData.saveDataComponentPB(actorItem);
	return 0;
}

int ClientItem::LoadFromPB(const PB_GeneralEnterAOIHC& pb)
{
	const PB_ActorItem& actorItem = pb.actoritem();
	return loadItemFromPB(actorItem);
}

int ClientItem::loadItemFromPB(const PB_ActorItem& actorItem)
{
	const PB_ActorCommon& actorCommon = actorItem.basedata();
	int ret = loadPBActorCommon(actorCommon);
	if (ret != 0)
		return ret;
	int itemId = actorItem.itemid();
	int dur = actorItem.durable();
	int toughness = -1;
	if (actorItem.has_toughness())
	{
		int toughness = actorItem.toughness();
	}
	if (itemId > 0 && GetDefManagerProxy()->getItemDef(itemId) == NULL)	//检查mod, 被删掉则替换成默认物品
	{
		itemId = ITEM_DEFAULT;
		dur = -1;
		toughness = -1;
	}
	if (itemId >= 0 && GetDefManagerProxy()->getItemDef(itemId))
	{
		std::string userdatastr = "";
		if (actorItem.has_userdatastr())
		{
			if (IsDyeableBlock(itemId))
			{
				//染色方块同步  by:Jeff 2023/2/2
				int colordata = 0;
				sscanf(actorItem.userdatastr().c_str(), "%d", &colordata);
				m_ProtoData = colordata;
			}
			else
			{
				userdatastr = actorItem.userdatastr();
			}

		}

		// 这里把userdatastr加上去了
		SetBackPackGrid(m_ItemData, itemId, actorItem.num(), dur, toughness, 0, 1, 0, userdatastr.c_str());
		m_ItemData.setMaxDuration(actorItem.maxdurable());

		int n = actorItem.enchants_size();
		if (n > MAX_ITEM_ENCHANTS) n = MAX_ITEM_ENCHANTS;
		int enchants[MAX_ITEM_ENCHANTS];
		for (int i = 0; i < n; i++)
		{
			enchants[i] = actorItem.enchants(i);
		}
		m_ItemData.setEnchants(n, enchants);
		m_ItemData.getRuneData().load(actorItem.runes(), actorItem.runes_size());
	}
	m_DelayPickTicks = actorItem.delayticks();

	// 加载服务器id
	if (actorItem.has_serverid())
	{
		m_ServerID = actorItem.serverid();
	}

	m_ItemData.loadDataComponentPB(actorItem);
	return 0;
}

flatbuffers::Offset<FBSave::SectionActor> ClientItem::save(SAVE_BUFFER_BUILDER& builder)
{
	auto item = saveItem(builder);
	return saveSectionActor(builder, FBSave::SectionActorUnion_ActorItem, item.Union());
}

flatbuffers::Offset<FBSave::ActorItem> ClientItem::saveItem(SAVE_BUFFER_BUILDER &builder)
{
	auto basedata = saveActorCommon(builder);
	auto enchants = builder.CreateVector(m_ItemData.getEnchants(), m_ItemData.getNumEnchant());
	auto runes = m_ItemData.getRuneData().save(builder);

	flatbuffers::Offset<flatbuffers::Vector<int8_t>> componentsOffset;
	m_ItemData.saveDataComponent(builder, componentsOffset);
	if(m_ItemData.def)
	{
		// vehicle类型掉落 特殊处理 保存userdatastr数据
		return FBSave::CreateActorItem(builder, basedata, m_ItemData.def->ID, m_ItemData.getNum(), m_ItemData.getDuration(), m_DelayPickTicks, enchants,builder.CreateString(m_ItemData.getUserdataStr().c_str()), builder.CreateString(m_ServerID.c_str()), runes, m_creatorId, m_ItemData.getToughness(), componentsOffset);
	}
	return FBSave::CreateActorItem(builder, basedata, 224, 1, m_ItemData.getDuration(), m_DelayPickTicks, enchants, 0, 
		builder.CreateString(m_ServerID.c_str()),runes, m_creatorId, m_ItemData.getToughness(), componentsOffset, m_ItemData.getMaxDuration());
}

bool ClientItem::load(const void *srcdata, int version)
{
	auto src = reinterpret_cast<const FBSave::ActorItem *>(srcdata);
	loadActorCommon(src->basedata());

	int itemId = src->itemid();
	int dur = src->durable();
	int toughness = src->toughness();

	if(itemId > 0 && GetDefManagerProxy()->getItemDef(itemId) == NULL)	//检查mod, 被删掉则替换成默认物品
	{
		itemId = ITEM_DEFAULT;
		dur = -1;
		toughness = -1;
	}

	if(itemId >= 0 && GetDefManagerProxy()->getItemDef(itemId))
	{
		std::string userdatastr = "";
		if (src->userdatastr())
		{
			userdatastr = src->userdatastr()->str();
		}
		
		// 这里把userdatastr加上去了
		SetBackPackGrid(m_ItemData, itemId, src->num(), dur, toughness, 0, 1 ,0 , userdatastr.c_str());
		m_ItemData.setMaxDuration(src->maxdurable());

		int n = src->enchants()->size();
		if(n > MAX_ITEM_ENCHANTS) n = MAX_ITEM_ENCHANTS;
		int enchants[MAX_ITEM_ENCHANTS];
		for(int i=0; i<n; i++)
		{
			enchants[i] = src->enchants()->Get(i);
		}
		m_ItemData.setEnchants(n, enchants);
		m_ItemData.getRuneData().load(src->runes());

		m_ItemData.loadDataComponent(src->datacomponents());
	}
	m_DelayPickTicks = src->delayticks();

	// 加载服务器id
	if (src->serverid())
	{
		m_ServerID = src->serverid()->str();
	}
	return true;
}

EQUIP_SLOT_TYPE ClientItem::getItemArmorPosition()
{
	EQUIP_SLOT_TYPE slot = MAX_EQUIP_SLOTS;
	if (!m_ItemData.def) { return slot; }//这里指针要判空

	const ToolDef *tooldef = GetDefManagerProxy()->getToolDef(m_ItemData.def->ID);
	if(tooldef)
	{
		if(tooldef->Type == 8) slot = EQUIP_HEAD;
		else if(tooldef->Type == 9) slot = EQUIP_BREAST;
		else if(tooldef->Type == 10) slot = EQUIP_LEGGING;
		else if(tooldef->Type == 11) slot = EQUIP_SHOE;
		else if(tooldef->Type == 16) slot = EQUIP_PIFENG;
		else if(tooldef->Type == 6) slot = EQUIP_WEAPON;
		else if(tooldef->Type == 35) slot = EQUIP_HEAD_LINING;
		else if(tooldef->Type == 36) slot = EQUIP_BREAST_LINING;
		else if(tooldef->Type == 37) slot = EQUIP_LEGGING_LINING;
		else if(tooldef->Type == 38) slot = EQUIP_SHOE_LINING;
	}
	return slot;
}

std::string ClientItem::getServerID()
{
	return m_ServerID;
}

void ClientItem::setServerID(std::string& serverid)
{
	m_ServerID = serverid;
}

void ClientItem::setPickOnce(bool picked)
{
	m_PickOnce = picked;
}

void ClientItem::setUserData(const std::string& str)
{
	m_ItemData.userdata_str = str;
}

void ClientItem::setEditStatus(bool isEdit)
{
	m_isEditing = isEdit;
}

void ClientItem::setScale(float s)
{
	ItemDef* def = GetDefManagerProxy()->getItemDef(getItemID());
	if (def && m_RenderObjs.size() > 0)
	{
		float defscal = def->DropScale > 0.00001 ? def->DropScale : 1;
		s = s_ItemModelScales[m_RenderObjs[0]->getModelType()][ITEM_MODELDISP_DROP] * defscal * s;
		for (size_t i = 0; i < m_RenderObjs.size(); i++)
		{
			m_RenderObjs[i]->SetScale(Vector3f(s,s,s));
		}
	}
}

void ClientItem::updateRenderObjs()
{
	clearRenderObjs();
	createRenderObjs();
}

void ClientItem::SetItemSpawnType(int type)
{
	if (type > 0 && type < 8)
	{
		m_spwantype = static_cast<SPAWNITEMTYPE>(type);
	}
	else
	{
		m_spwantype = ANYWAY;
	}
}

SPAWNITEMTYPE ClientItem::GetItemSpawnType()
{
	return m_spwantype;
}
//道具播放特效
void ClientItem::playMotion(const int& quality_Level_color,const char* name, bool reset_play, int motion_class, float fscale,float looptime)
{
	if (m_RenderObjs.size() > 0 && ITEM_MODEL_OMOD == m_RenderObjs[0]->getModelType())
	{
		m_RenderObjs[0]->playMotion(quality_Level_color,name, reset_play, motion_class, fscale, looptime);
	}
	else
	{
		if (!m_particleEntity)
		{
			m_particleEntity = Entity::Create();
			m_particleEntity->GetGameObject()->SetLayer(Rainbow::kLayerIndexCustom_DropItem);
			m_particleEntity->GetTransform()->SetParent(m_RootObj->GetTransform());
		}
		m_particleEntity->PlayMotion(name, reset_play, motion_class, looptime);
		m_particleEntity->SetMotionScale(name, fscale);
	}
}
//设置特效大小
void ClientItem::setMotionScale(const char* name, float fScale)
{
	if (m_RenderObjs.size() > 0 && ITEM_MODEL_OMOD == m_RenderObjs[0]->getModelType())
	{
		ModelItemMesh* mesh = dynamic_cast<ModelItemMesh*>(m_RenderObjs[0]);
		if (mesh && mesh->getEntity())
		{
			mesh->getEntity()->SetMotionScale(name, fScale);
		}
	}
	else
	{
		if (m_particleEntity)
		{
			m_particleEntity->SetMotionScale(name, fScale);
		}

	}
}
//道具删除特效
void ClientItem::stopMotion(const char* fxname)
{
	if (m_RenderObjs.size() > 0 && ITEM_MODEL_OMOD == m_RenderObjs[0]->getModelType())
	{
		ModelItemMesh* mesh = dynamic_cast<ModelItemMesh*>(m_RenderObjs[0]);
		if (mesh && mesh->getEntity())
		{
			mesh->getEntity()->StopMotion(fxname);
		}
	}
	else
	{
		if (m_particleEntity)
		{
			m_particleEntity->StopMotion(fxname);
		}
	}
}

int ClientItem::getMotionCount()
{
	if (m_RenderObjs.size() > 0 && ITEM_MODEL_OMOD == m_RenderObjs[0]->getModelType())
	{
		ModelItemMesh* mesh = dynamic_cast<ModelItemMesh*>(m_RenderObjs[0]);
		if (mesh && mesh->getEntity())
		{
			return mesh->getEntity()->GetMotionCount();
		}
	}
	else
	{
		if (m_particleEntity)
		{
			return m_particleEntity->GetMotionCount();
		}
	}

	return 0;
}
const char* ClientItem::getMotionNameByIndex(int index, int class_type)
{
	if (m_RenderObjs.size() > 0 && ITEM_MODEL_OMOD == m_RenderObjs[0]->getModelType())
	{
		ModelItemMesh* mesh = dynamic_cast<ModelItemMesh*>(m_RenderObjs[0]);
		if (mesh && mesh->getEntity())
		{
			return mesh->getEntity()->GetMotionNameByIndex(index, class_type);
		}
	}
	else
	{
		if (m_particleEntity)
		{
			return  m_particleEntity->GetMotionNameByIndex(index, class_type);
		}
	}

	return NULL;
}
float ClientItem::getMotionScale(const char* name)
{
	if (m_RenderObjs.size() > 0 && ITEM_MODEL_OMOD == m_RenderObjs[0]->getModelType())
	{
		ModelItemMesh* mesh = dynamic_cast<ModelItemMesh*>(m_RenderObjs[0]);
		if (mesh && mesh->getEntity())
		{
			return mesh->getEntity()->GetMotionScale(name).x;
		}
	}
	else 
	{
		if (m_particleEntity)
		{
			return m_particleEntity->GetMotionScale(name).x;
		}
	}
	return 1;
}
bool ClientItem::playAct(int act, int playmode) // 投掷物引用带动作的微缩模型播放动画
{
	m_TriggerAnimID = act;
	m_AnimMode = playmode;
	if (m_RenderObjs.size() > 0 && ITEM_MODEL_OMOD == m_RenderObjs[0]->getModelType())
	{
		ModelItemMesh* mesh = dynamic_cast<ModelItemMesh*>(m_RenderObjs[0]);
		if (mesh && mesh->getEntity())
		{
			auto def = GetDefManagerProxy()->getTriggerActDef(act);
			if (def)
			{
				act = def->ActID;
			}
			//mesh->LoadAllCustomAnim(act, playmode);
			if (mesh->getEntity())
			{
				mesh->getEntity()->PlayAnim(act, playmode);
			}
		}
		return true;
	}
	return false;
}

std::string  ClientItem::getFullyCustomModelKey()
{
	ItemDef* itemdef = GetDefManagerProxy()->getItemDef(getItemID());
	if (itemdef && (itemdef->MeshType == OMOD_GEN_MESH || itemdef->MeshType == CUSTOM_GEN_MESH || itemdef->MeshType == FULLY_CUSTOM_GEN_MESH || itemdef->MeshType == IMPORT_MODEL_GEN_MESH))
	{
		return itemdef->Model.c_str();
	}
	return "";
}

//刷新下载模型
void ClientItem::updateBodyByFullyCustomModel()
{
	clearRenderObjs();
	createRenderObjs();
}

IActorLocoMotion* ClientItem::GetItemLocoMotion()
{
	return getLocoMotion();
}