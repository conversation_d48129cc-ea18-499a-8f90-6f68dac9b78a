/**
* file : miniSandboxBlockManualToLua
* func : ɳ��tolua�ֶ�
* by ; chenzihang
*/
#ifndef __MINI_SANDBOX_BLOCK_MANUAL_TOLUA_H__
#define __MINI_SANDBOX_BLOCK_MANUAL_TOLUA_H__

#include "SandboxMacros.h"
#include "SandboxCoreLuaDirector.h"
#include "SandboxCoreDriver.h"
#include "blocks/BlockMaterial.h"

extern "C"
{
#include "lua.h"
#include "lauxlib.h"
#include "lualib.h"
#include "lua_cjson.h"
}
#include "Minitolua.h"


using namespace MNSandbox;


extern int tolua_miniSandboxBlock_open(lua_State*);


static const unsigned s_tempLuaStrSize1 = 256; // �ַ��������С
static char s_tmpLuaString1[s_tempLuaStrSize1]; // �ַ�������


/**
* ���ض��巽��
*/
// BlockMaterial



/**
* types
*/
static void tolua_reg_types(lua_State* tolua_S)
{
	//tolua_usertype(tolua_S,"xxxxx");
}

/**
* open
*/
int sandboxcoreblock_open_manual_tolua(lua_State* tolua_S)
{
	tolua_miniSandboxBlock_open(tolua_S);

	int top = lua_gettop(tolua_S);

	tolua_open(tolua_S);
	tolua_reg_types(tolua_S);
	tolua_module(tolua_S, NULL, 0);
	tolua_beginmodule(tolua_S, NULL);
	{
		//tolua_beginmodule(tolua_S, "BlockMaterial");
		//{
		//}
		//tolua_endmodule(tolua_S);
	}
	tolua_endmodule(tolua_S);

	lua_settop(tolua_S, top);
	return 1;
}

#endif
