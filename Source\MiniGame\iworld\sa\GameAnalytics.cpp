#include "GameAnalytics.h"

#include <iostream>
#include <type_traits>

using namespace thinkingdata;

// 全局变量定义
GameAnalytics* g_pGameAnalytics = nullptr;

GameAnalytics::CommonProperties GameAnalytics::s_commonProps;
bool GameAnalytics::m_initialized = false;

// 构造函数和析构函数实现
GameAnalytics::GameAnalytics() {
  // 构造函数实现
}

GameAnalytics::~GameAnalytics() {
  // 析构函数实现
}

// 初始化方法实现
bool GameAnalytics::Init(const std::string& device_id, int env) {
  // 如果全局变量已经初始化，直接返回true
  if (g_pGameAnalytics != nullptr) {
    return true;
  }
  
  // 创建全局对象
  g_pGameAnalytics = new GameAnalytics();
  
  // 初始化埋点SDK
  std::string appid = "a12c62532cf54941ba8cb3cb63784b07"; // 数据文件路径
  std::string server_url = "https://tga.mini1.cn";
  bool is_login_id = false; // device_id 不是登录ID
  int max_staging_record_count = 10000; // 最大暂存记录数

  // 配置ThinkingData SDK
  TDConfig td_config;
  td_config.appid = appid;
  td_config.server_url = server_url;
  td_config.enableAutoCalibrated = true; // 自动时间校准
  td_config.mode = TDMode::TD_NORMAL;
  td_config.databaseLimit = max_staging_record_count;
  td_config.dataExpression = 15;

  bool success = ThinkingAnalyticsAPI::Init(td_config);
  
  if (success) {
    m_initialized = true;
    s_commonProps.env = env;                // 设置环境
    s_commonProps.device_id = device_id;    // 设置设备ID
    
    // 可以在这里设置其他默认属性
  }

  ThinkingAnalyticsAPI::EnableLog(true);
  
  return success;
}



void GameAnalytics::SetSessionInfo(const std::string& session_id, int64_t session_start_time) {
    s_commonProps.session_id = session_id;
    s_commonProps.session_start_time = session_start_time;
}

void GameAnalytics::SetCommonInfo(const std::string& device_id,
                                const std::string& uin,
                                const std::string& log_id,
                                const std::string& ip_address,
                                const std::string& app_version,
                                int apn,
                                const std::string& os_type,
                                const std::string& country,
                                const std::string& province) {

    if (device_id.empty()) {
        return;
    }
    
    s_commonProps.device_id = device_id;
    s_commonProps.uin = uin;
    
    if (!log_id.empty()) {
        s_commonProps.log_id = log_id;
    }
    
    if (!ip_address.empty()) {
        s_commonProps.ip_address = ip_address;
    }
    
    if (!app_version.empty()) {
        s_commonProps.app_version = app_version;
    }
    
    s_commonProps.apn = apn;
    
    if (!os_type.empty()) {
        s_commonProps.os_type = os_type;
    }
    
    if (!country.empty()) {
        s_commonProps.country = country;
    }
    
    if (!province.empty()) {
        s_commonProps.province = province;
    }
}

void GameAnalytics::SetGameSessionInfo(const std::string& game_session_id,
                                    int64_t game_session_start_time,
                                    int64_t sunshine_num) {
    s_commonProps.game_session_id = game_session_id;
    s_commonProps.game_session_start_time = game_session_start_time;
    s_commonProps.sunshine_num = sunshine_num;
}

void GameAnalytics::Login(const std::string& login_id) {
    ThinkingAnalyticsAPI::Login(login_id);
}

void GameAnalytics::Logout() {
    m_initialized = false;
    // 调用SDK登出
    ThinkingAnalyticsAPI::LogOut();
}

void GameAnalytics::TrackEvent(const std::string& event_name,
                             const std::string& param1,
                             const std::string& param2,
                             const std::string& param3,
                             int num_param1,
                             int num_param2,
                             bool bool_param1) {
    if (!m_initialized) {
        std::cout << "[GameAnalytics] Not initialized" << std::endl;
        return;
    }

    if (event_name.empty()) {
        return;
    }
    
    thinkingdata::TDJSONObject properties = createCommonProperties();
    
    if (!param1.empty()) {
        properties.SetString("param1", param1);
    }
    
    if (!param2.empty()) {
        properties.SetString("param2", param2);
    }
    
    if (!param3.empty()) {
        properties.SetString("param3", param3);
    }
    
    if (num_param1 != 0) {  // 假设0表示无效值
        properties.SetNumber("num_param1", num_param1);
    }
    
    if (num_param2 != 0) {  // 假设0表示无效值
        properties.SetNumber("num_param2", num_param2);
    }
    
    if (bool_param1) {
        properties.SetBool("bool_param1", bool_param1);
    }
    
    ThinkingAnalyticsAPI::Track(event_name, properties);
    ThinkingAnalyticsAPI::Flush();
}

void GameAnalytics::TrackPlayerCreated(const std::string& character_id, const std::string& character_class) {
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("character_id", character_id);
    properties.SetString("character_class", character_class);
    ThinkingAnalyticsAPI::Track("PlayerCreated", properties);
}

void GameAnalytics::TrackPlayerLoaded(const std::string& character_id) {
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("character_id", character_id);
    ThinkingAnalyticsAPI::Track("PlayerLoaded", properties);
}

void GameAnalytics::TrackResourceGathered(const std::string& resource_type, const std::string& item_id, int amount, const std::string& gather_method) {
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("resource_type", resource_type);
    properties.SetString("item_id", item_id);
    properties.SetNumber("amount", amount);
    properties.SetString("gather_method", gather_method);
    ThinkingAnalyticsAPI::Track("ResourceGathered", properties);
}

void GameAnalytics::TrackResourceConsumed(const std::string& resource_type, const std::string& item_id, int amount, const std::string& consume_reason) {
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("resource_type", resource_type);
    properties.SetString("item_id", item_id);
    properties.SetNumber("amount", amount);
    properties.SetString("consume_reason", consume_reason);
    ThinkingAnalyticsAPI::Track("ResourceConsumed", properties);
}
void GameAnalytics::TrackItemCrafted(const std::string& item_id, const std::string& item_type, int amount){
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("item_id", item_id);
    properties.SetString("item_type", item_type);
    properties.SetNumber("amount", amount);
    ThinkingAnalyticsAPI::Track("ItemCrafted", properties);
}

void GameAnalytics::TrackItemUsed(const std::string& item_id, const std::string& item_type, const std::string& use_target) {
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("item_id", item_id);
    properties.SetString("item_type", item_type);
    properties.SetString("use_target", use_target);
    ThinkingAnalyticsAPI::Track("ItemUsed", properties);
}

void GameAnalytics::TrackItemTraded(const std::string& item_id, const std::string& item_type, int amount, const std::string& trade_partner)
{
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("item_id", item_id);
    properties.SetString("item_type", item_type);
    properties.SetNumber("amount", amount);
    properties.SetString("trade_partner", trade_partner);
    ThinkingAnalyticsAPI::Track("ItemTraded", properties);
}

void GameAnalytics::TrackBuildingPlaced(const std::string& building_id, const std::string& building_type, int x, int y, int z)
{
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("building_id", building_id);
    properties.SetString("building_type", building_type);
    properties.SetNumber("x", x);
    properties.SetNumber("y", y);
    properties.SetNumber("z", z);
    ThinkingAnalyticsAPI::Track("BuildingPlaced", properties);
}

void GameAnalytics::TrackBuildingUpgraded(const std::string& building_id, const std::string& building_type, int level)
{
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("building_id", building_id);
    properties.SetString("building_type", building_type);
    properties.SetNumber("level", level);
    ThinkingAnalyticsAPI::Track("BuildingUpgraded", properties);
}

void GameAnalytics::TrackBuildingDestroyed(const std::string& building_id, const std::string& building_type, const std::string& reason)
{
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("building_id", building_id);
    properties.SetString("building_type", building_type);
    properties.SetString("reason", reason);
    ThinkingAnalyticsAPI::Track("BuildingDestroyed", properties);
}

void GameAnalytics::TrackCombatStarted(const std::string& enemy_type, const std::string& combat_type, bool is_pvp) {
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("enemy_type", enemy_type);
    properties.SetString("combat_type", combat_type);
    properties.SetBool("is_pvp", is_pvp);
    ThinkingAnalyticsAPI::Track("CombatStarted", properties);
}

void GameAnalytics::TrackCombatEnded(const std::string& enemy_type, const std::string& combat_type, bool is_pvp, const std::string& result, int duration_seconds) {
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("enemy_type", enemy_type);
    properties.SetString("combat_type", combat_type);
    properties.SetBool("is_pvp", is_pvp);
    properties.SetString("result", result);
    properties.SetNumber("duration_seconds", duration_seconds);
    ThinkingAnalyticsAPI::Track("CombatEnded", properties);
}

void GameAnalytics::TrackPlayerDied(const std::string& cause_of_death, const std::string& killer_type, const std::string& killer_name) {
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("cause_of_death", cause_of_death);
    properties.SetString("killer_type", killer_type);
    properties.SetString("killer_name", killer_name);
    ThinkingAnalyticsAPI::Track("PlayerDied", properties);
}

void GameAnalytics::TrackChatMessageSent(const std::string& channel, const std::string& message_type) {
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("channel", channel);
    properties.SetString("message_type", message_type);
    ThinkingAnalyticsAPI::Track("ChatMessageSent", properties);
}

void GameAnalytics::TrackTeamCreated(const std::string& team_id) {
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("team_id", team_id);
    ThinkingAnalyticsAPI::Track("TeamCreated", properties);
}

void GameAnalytics::TrackTeamJoined(const std::string& team_id) {
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("team_id", team_id);
    ThinkingAnalyticsAPI::Track("TeamJoined", properties);
}

void GameAnalytics::TrackTeamLeft(const std::string& team_id, const std::string& reason) {
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("team_id", team_id);
    properties.SetString("reason", reason);
    ThinkingAnalyticsAPI::Track("TeamLeft", properties);
}

void GameAnalytics::TrackFriendAdded(const std::string& friend_id){
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("friend_id", friend_id);
    ThinkingAnalyticsAPI::Track("FriendAdded", properties);
}

void GameAnalytics::TrackFriendRemoved(const std::string& friend_id){
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("friend_id", friend_id);
    ThinkingAnalyticsAPI::Track("FriendRemoved", properties);
}

void GameAnalytics::TrackUIClick(const std::string& ui_name, const std::string& button_name) {
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("ui_name", ui_name);
    properties.SetString("button_name", button_name);
    ThinkingAnalyticsAPI::Track("UIClick", properties);
}

void GameAnalytics::TrackUIOpen(const std::string& ui_name) {
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("ui_name", ui_name);
    ThinkingAnalyticsAPI::Track("UIOpen", properties);
}

void  GameAnalytics::TrackActivityStarted(const std::string& activity_id, const std::string& activity_type, const std::string& activity_name) {
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("activity_id", activity_id);
    properties.SetString("activity_type", activity_type);
    properties.SetString("activity_name", activity_name);
    ThinkingAnalyticsAPI::Track("ActivityStarted", properties);
}

void GameAnalytics::TrackActivityProgress(const std::string& activity_id, const std::string& activity_type, const std::string& activity_name, int progress, const std::string& param1) {
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("activity_id", activity_id);
    properties.SetString("activity_type", activity_type);
    properties.SetString("activity_name", activity_name);
    properties.SetNumber("progress", progress);
    properties.SetString("param1", param1);
    ThinkingAnalyticsAPI::Track("ActivityProgress", properties);
}

void GameAnalytics::TrackActivityCompleted(const std::string& activity_id, const std::string& activity_type, const std::string& activity_name, const std::string& result) {
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("activity_id", activity_id);
    properties.SetString("activity_type", activity_type);
    properties.SetString("activity_name", activity_name);
    properties.SetString("result", result);
    ThinkingAnalyticsAPI::Track("ActivityCompleted", properties);
}

void GameAnalytics::TrackStorePageViewed(const std::string& page_name) {
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("page_name", page_name);
    ThinkingAnalyticsAPI::Track("StorePageViewed", properties);
}

void GameAnalytics::TrackStoreItemClicked(const std::string& item_id, const std::string& item_name, const std::string& item_type, const std::string& currency_type, double price) {
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("item_id", item_id);
    properties.SetString("item_name", item_name);
    properties.SetString("item_type", item_type);
    properties.SetString("currency_type", currency_type);
    properties.SetNumber("price", price);
    ThinkingAnalyticsAPI::Track("StoreItemClicked", properties);
}

void GameAnalytics::TrackStorePurchase(const std::string& item_id, const std::string& item_name, const std::string& item_type, const std::string& currency_type, double price, int amount, const std::string& payment_method, const std::string& param1) {
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("item_id", item_id);
    properties.SetString("item_name", item_name);
    properties.SetString("item_type", item_type);
    properties.SetString("currency_type", currency_type);
    properties.SetNumber("price", price);
    properties.SetNumber("amount", amount);
    properties.SetString("payment_method", payment_method);
    properties.SetString("param1", param1);
    ThinkingAnalyticsAPI::Track("StorePurchase", properties);
}

thinkingdata::TDJSONObject GameAnalytics::createCommonProperties() {
    thinkingdata::TDJSONObject properties;

    properties.SetString("session_id", s_commonProps.session_id);
    properties.SetNumber("session_start_time", s_commonProps.session_start_time);
    
    properties.SetNumber("env", s_commonProps.env);
    properties.SetString("ip_address", s_commonProps.ip_address);
    properties.SetString("app_version", s_commonProps.app_version);
    properties.SetNumber("apn", s_commonProps.apn);
    properties.SetString("os_type", s_commonProps.os_type);
    properties.SetString("country", s_commonProps.country);
    properties.SetString("province", s_commonProps.province);
    
    properties.SetString("device_id", s_commonProps.device_id);
    properties.SetString("uin", s_commonProps.uin);
    properties.SetString("log_id", s_commonProps.log_id);
    
    properties.SetString("game_session_id", s_commonProps.game_session_id);
    properties.SetNumber("game_session_start_time", s_commonProps.game_session_start_time);
    properties.SetNumber("sunshine_num", s_commonProps.sunshine_num);

    return properties;
}


// 模板函数实现
template<typename T>
void GameAnalytics::SetUserProfile(const std::string& property_name, const T& value) {
    TDJSONObject userProps;
    if constexpr (std::is_same_v<T, std::string>) {
        userProps.SetString(property_name, value);
    } else if constexpr (std::is_same_v<T, int>) {
        userProps.SetNumber(property_name, value);
    } else if constexpr (std::is_same_v<T, bool>) {
        userProps.SetBool(property_name, value);
    } else if constexpr (std::is_same_v<T, double> || std::is_same_v<T, float>) {
        userProps.SetNumber(property_name, static_cast<double>(value));
    }
    ThinkingAnalyticsAPI::UserSet(userProps);
}

// 显式实例化常用类型
template void GameAnalytics::SetUserProfile<std::string>(const std::string&, const std::string&);
template void GameAnalytics::SetUserProfile<int>(const std::string&, const int&);
template void GameAnalytics::SetUserProfile<bool>(const std::string&, const bool&);
template void GameAnalytics::SetUserProfile<double>(const std::string&, const double&);
template void GameAnalytics::SetUserProfile<float>(const std::string&, const float&);
